package com.starfruit.immofish.listener;

import com.starfruit.immofish.ImmoFish;
import com.starfruit.immofish.fish.Fish;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerItemConsumeEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.potion.PotionEffect;

public class PlayerEatListener implements Listener {

    @EventHandler
    public void onPlayerEat(PlayerItemConsumeEvent event) {
        ItemStack item = event.getItem();
        Player player = event.getPlayer();

        if (item.getItemMeta() == null || !item.hasItemMeta()) return;

        ItemMeta meta = item.getItemMeta();

        if (!meta.getPersistentDataContainer().has(ImmoFish.IMMO_FISH_KEY, PersistentDataType.STRING)) return;

        String fishId = meta.getPersistentDataContainer().get(ImmoFish.IMMO_FISH_KEY, PersistentDataType.STRING);

        Fish fish = ImmoFish.getInstance().getFishManager().getFishItem(fishId);
        if (fish == null) return;

        for (PotionEffect effect : fish.getEffects()) {
            player.addPotionEffect(effect);
        }
    }
}
