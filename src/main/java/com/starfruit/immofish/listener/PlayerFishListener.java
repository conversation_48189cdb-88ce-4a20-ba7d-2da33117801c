package com.starfruit.immofish.listener;

import com.muhammaddaffa.mdlib.utils.Common;
import com.muhammaddaffa.mdlib.utils.Logger;
import com.starfruit.immofish.ImmoFish;
import org.bukkit.NamespacedKey;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.FishHook;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.persistence.PersistentDataType;

import java.util.concurrent.ThreadLocalRandom;

public class PlayerFishListener implements Listener {

    @EventHandler
    public void onPlayerFish(PlayerFishEvent event) {
        PlayerFishEvent.State state = event.getState();
        FishHook hook = event.getHook();

        // check if the hook has already been rolled
        var rolled = new NamespacedKey(ImmoFish.getInstance(), "ROLLED");
        var win = new NamespacedKey(ImmoFish.getInstance(), "WIN");

        var pdc = hook.getPersistentDataContainer();

        FileConfiguration config = ImmoFish.DEFAULT_CONFIG.getConfig();

        int chance = Math.max(0, Math.min(100, config.getInt("global-chance", 0)));

        switch (state) {
            case BITE -> {
                if (!pdc.has(rolled, PersistentDataType.BYTE)) {
                    boolean wins = ThreadLocalRandom.current().nextInt(100) < chance;
                    pdc.set(rolled, PersistentDataType.BYTE, (byte) 1);
                    pdc.set(win, PersistentDataType.BYTE, (byte) (wins ? 1 : 0));
                }
            }
            case CAUGHT_FISH -> {
                boolean wins = pdc.getOrDefault(win, PersistentDataType.BYTE, (byte) 0) == 1;

                if (wins) {
                    // Blok vanilla drop dan kasih custom fish
                    event.setCancelled(true);

                    // Optional: buang entitas hasil tangkapan vanilla kalau ada
                    if (event.getCaught() != null) {
                        event.getCaught().remove();
                    }

                    Common.addInventoryItem(
                            event.getPlayer(),
                            ImmoFish.getInstance().getFishManager().getRandomFish(event.getPlayer()).getItem()
                    );

                    event.getHook().remove();
                }
                // Bersihkan flag setelah selesai (satu lemparan berakhir di CAUGHT_FISH)
                pdc.remove(rolled);
                pdc.remove(win);
            }
            default -> {
                pdc.remove(rolled);
                pdc.remove(win);
            }
        }
    }
}
