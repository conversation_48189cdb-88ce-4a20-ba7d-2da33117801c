package com.starfruit.immofish.fish;

import com.muhammaddaffa.mdlib.utils.Common;
import com.muhammaddaffa.mdlib.utils.Logger;
import com.starfruit.immofish.ImmoFish;
import com.starfruit.immofish.utils.Utils;
import org.bukkit.block.Biome;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class FishManager {

    private final Map<String, Fish> fish = new HashMap<>();

    public Fish getFishItem(String key) {
        return fish.get(key);
    }

    public Fish getRandomFish(Player player) {
        // get the world time
        int timeRealLife = LocalDateTime.now().getHour();
        long time = player.getWorld().getTime();

        // get all the fish that can be caught at this time
        List<Fish> candidate = new ArrayList<>();
        for (Fish fishy : this.fish.values()) {
            // if the fish can be caught at any time, add it to the candidate
            if (fishy.isAnyTime() || time >= fishy.getTime() && player.getWorld().getBiome(player.getLocation()).equals(fishy.getBiomes())) {
                // add the fish to the candidate
                candidate.add(fishy);
            }
        }

        if (candidate.isEmpty()) {
            return getRandomFish(player);
        }

        Fish fish1 = candidate.get(ThreadLocalRandom.current().nextInt(candidate.size()));
        if (ThreadLocalRandom.current().nextInt(100) >= fish1.getChance()) {
            return getRandomFish(player);
        }

        return fish1;
    }

    public List<Fish> getFish() {
        return new ArrayList<>(fish.values());
    }

    public void loadFish() {
        this.fish.clear();
        FileConfiguration config = ImmoFish.DEFAULT_CONFIG.getConfig();
        for (String key : config.getConfigurationSection("fish").getKeys(false)) {
            List<PotionEffect> effects = new ArrayList<>();

            ItemStack stack = Utils.buildItemStack(config, "fish." + key + ".item");
            int chance = config.getInt("fish." + key + ".chance");

            for (String line : config.getStringList("fish." + key + ".effects")) {
                // scrap the string to get the potion type and amplifier
                String[] split = line.split(";");
                PotionEffectType type = PotionEffectType.getByName(split[0].toUpperCase());
                int duration = Integer.parseInt(split[1]);
                int amplifier = Integer.parseInt(split[2]);
                // if there is no potion with that type, skip
                if (type == null) {
                    continue;
                }
                // create the potion effect instance
                // and add it to the list of effects
                effects.add(new PotionEffect(type, duration, amplifier, false, false, false));
            }

            int time = config.getInt("fish." + key + ".time");
            String timeString = String.valueOf(time);
            String[] timeSplit = timeString.split("-");
            if (timeSplit.length == 2 && !timeString.contains("-")) {
                int min = Integer.parseInt(timeSplit[0]);
                int max = Integer.parseInt(timeSplit[1]);
                time = ThreadLocalRandom.current().nextInt(min, max + 1);
            }
            boolean anytime = (time == 0);

            List<Biome> biomes = new ArrayList<>();
            List<String> biomeStrings = config.getStringList("fish." + key + ".biomes");

            // Check if "any" is specified for biomes
            boolean anyBiome = biomeStrings.contains("any") || biomeStrings.contains("ANY");

            if (anyBiome) {
                // Add all available biomes
                biomes.addAll(Arrays.asList(Biome.values()));
            } else {
                // Add specific biomes from config
                for (String biome : biomeStrings) {
                    biomes.add(getBiome(biome));
                }
            }

            Logger.info("Loaded fish " + key);

            this.fish.put(key, new Fish(key, stack, effects, chance, anytime, time, biomes));
        }
    }

    private Biome getBiome(String biome) {
        try {
            return Biome.valueOf(biome.toUpperCase());
        } catch (IllegalArgumentException e) {
            return Biome.PLAINS;
        }
    }
}
