package com.starfruit.immofish.fish;

import com.muhammaddaffa.mdlib.utils.ItemBuilder;
import com.muhammaddaffa.mdlib.utils.Logger;
import com.starfruit.immofish.ImmoFish;
import org.bukkit.block.Biome;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.potion.PotionEffect;

import java.util.List;

public class Fish {

    private final String id;
    private final ItemStack stack;
    private final List<PotionEffect> effects;
    private final int chance;
    private final boolean anyTime;
    private final int time;
    private final List<Biome> biomes;

    public Fish(String id, ItemStack stack, List<PotionEffect> effects, int chance, boolean anyTime, int time, List<Biome> biomes) {
        this.id = id;
        this.stack = stack;
        this.effects = effects;
        this.chance = chance;
        this.anyTime = anyTime;
        this.time = time;
        this.biomes = biomes;
    }

    public String getId() {
        return id;
    }

    public ItemStack getItem() {
        ItemBuilder builder = new ItemBuilder(stack.clone());
        Logger.info("Fish id: " + this.id);
        builder.pdc(ImmoFish.IMMO_FISH_KEY, this.id);
        Logger.info("Fish item: " + builder.build());
        return builder.build();
    }

    public List<PotionEffect> getEffects() {
        return effects;
    }

    public int getChance() {
        return chance;
    }

    public int getTime() {
        return time;
    }

    public boolean isAnyTime() {
        return anyTime;
    }

    public List<Biome> getBiomes() {
        return biomes;
    }
}
