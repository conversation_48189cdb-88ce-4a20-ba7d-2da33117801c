package com.starfruit.immofish.utils;

import com.muhammaddaffa.mdlib.utils.Common;
import com.muhammaddaffa.mdlib.utils.Logger;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.List;

public class Utils {
    public static ItemStack buildItemStack(FileConfiguration config, String path) {
        // get all the available variables
        String material_string = config.getString(path + ".material");
        int custom_model_data = config.getInt(path + ".custom_model_data");
        String display_name = config.getString(path + ".display_name");
        boolean glowing = config.getBoolean(path + ".glowing");
        List<String> lore = config.getStringList(path + ".lore");

        return buildItemStack(material_string, custom_model_data, display_name, glowing, lore);
    }

    public static ItemStack buildItemStack(String material_string, int custom_model_data, String display_name,
                                           boolean glowing, List<String> lore) {
        // start building the itemstack
        Material material = Material.matchMaterial(material_string);
        Logger.info("Material: " + material);
        if (material == null) {
            material = Material.DIRT;
        }
        ItemStack stack = new ItemStack(material, 1);
        ItemMeta meta = stack.getItemMeta();

        if (meta == null) {
            return stack;
        }

        meta.setCustomModelData(custom_model_data);
        meta.setDisplayName(Common.color(display_name));
        meta.setLore(Common.color(lore));
        if (glowing) {
            meta.addEnchant(Enchantment.DURABILITY, 1, true);
            meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        }

        stack.setItemMeta(meta);
        return stack;
    }
}
