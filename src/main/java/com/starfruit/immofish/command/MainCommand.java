package com.starfruit.immofish.command;

import com.muhammaddaffa.mdlib.utils.Config;
import com.starfruit.immofish.fish.FishManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class MainCommand implements CommandExecutor {

    private final FishManager manager;

    public MainCommand(FishManager manager) {
        this.manager = manager;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player player) || !player.hasPermission("immofish.admin")) {
            return false;
        }

        if (args[0].equalsIgnoreCase("reload")) {
            Config.reload();
            this.manager.loadFish();
            player.sendMessage("§aConfig reloaded");
            return true;
        }
        return false;
    }
}
