package com.starfruit.immofish;

import com.muhammaddaffa.mdlib.MDLib;
import com.muhammaddaffa.mdlib.utils.Config;
import com.starfruit.immofish.command.MainCommand;
import com.starfruit.immofish.command.TabComplete;
import com.starfruit.immofish.fish.FishManager;
import com.starfruit.immofish.listener.PlayerEatListener;
import com.starfruit.immofish.listener.PlayerFishListener;
import org.bukkit.NamespacedKey;
import org.bukkit.plugin.java.JavaPlugin;

public final class ImmoFish extends JavaPlugin {

    public static ImmoFish instance;
    public static Config DEFAULT_CONFIG;

    public static NamespacedKey IMMO_FISH_KEY;

    private final FishManager fishManager = new FishManager();

    @Override
    public void onLoad() {
        MDLib.inject(this);
    }

    @Override
    public void onEnable() {
        instance = this;
        MDLib.onEnable(this);
        DEFAULT_CONFIG = new Config( "config.yml", null, true);

        IMMO_FISH_KEY = new NamespacedKey(this, "immofish");

        fishManager.loadFish();

        getCommand("immofish").setExecutor(new MainCommand(this.fishManager));
        getCommand("immofish").setTabCompleter(new TabComplete());


        getServer().getPluginManager().registerEvents(new PlayerFishListener(), this);
        getServer().getPluginManager().registerEvents(new PlayerEatListener(), this);

    }

    @Override
    public void onDisable() {
        MDLib.shutdown();
    }

    public static ImmoFish getInstance() {
        return instance;
    }

    public FishManager getFishManager() {
        return fishManager;
    }
}
